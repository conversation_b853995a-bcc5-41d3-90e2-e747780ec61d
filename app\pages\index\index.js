let that = null,
	app = getApp(),
	config = app.globalData.config,
	cache = config.storage,
	helper = app.globalData.helper;

// 导入PC适配器
let pcAdapter = null;
try {
	pcAdapter = require('../../common/js/pc-adapter.js').default;
} catch (e) {
	console.log('PC适配器加载失败:', e);
}

export default {
	data() {
		return {
			images: [],
			appIsAudit: false,
			showWxGroup: false,
			showWxGroupText: '',
			appPlatform: app.globalData.appPlatform,
			showIndexShareName: '',
			articleList: [],
			currentCity: {},
			currentExam: {},
			currentProfession: {},
			menuList: [],
			examTime: {},
			isLoading: true,
			// PC端适配相关数据
			isPC: false,
			isPCWeixin: false,
			screenType: 'mobile',
			windowInfo: {}
		};
	},
	onLoad(options) {
		app.globalData.showShareMenu();
		that = this;
		// 初始化PC端适配
		that.initPCAdapter();
	},
	onShow(options) {
		that.getHomeData();
		that.initIndexExamData();
		// 重新检测PC环境（可能窗口大小发生变化）
		that.updatePCStatus();
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		/**
		 * 初始化PC端适配
		 */
		initPCAdapter() {
			try {
				if (pcAdapter) {
					// 获取PC环境信息
					const pcInfo = pcAdapter.getPCInfo();
					that.isPC = pcInfo.isPC;
					that.isPCWeixin = pcInfo.isPCWeixin;
					that.screenType = pcInfo.screenType;
					that.windowInfo = {
						windowWidth: pcInfo.windowWidth,
						windowHeight: pcInfo.windowHeight
					};

					console.log('首页PC适配初始化:', {
						isPC: that.isPC,
						isPCWeixin: that.isPCWeixin,
						screenType: that.screenType,
						windowInfo: that.windowInfo
					});

					// 监听窗口大小变化
					pcAdapter.onResize((info) => {
						that.handleWindowResize(info);
					});
				}
			} catch (error) {
				console.error('PC适配器初始化失败:', error);
			}
		},

		/**
		 * 更新PC状态
		 */
		updatePCStatus() {
			try {
				if (pcAdapter) {
					const pcInfo = pcAdapter.getPCInfo();
					that.isPC = pcInfo.isPC;
					that.isPCWeixin = pcInfo.isPCWeixin;
					that.screenType = pcInfo.screenType;
					that.windowInfo = {
						windowWidth: pcInfo.windowWidth,
						windowHeight: pcInfo.windowHeight
					};
				}
			} catch (error) {
				console.error('PC状态更新失败:', error);
			}
		},

		/**
		 * 处理窗口大小变化
		 */
		handleWindowResize(info) {
			console.log('首页窗口大小变化:', info);
			that.isPC = info.device.isPC;
			that.screenType = info.device.screenType;
			that.windowInfo = info.windowInfo;

			// 强制更新视图
			that.$forceUpdate();
		},

		/**
		 * 通用导航方法
		 * @param {String} url 完整的导航地址
		 */
		goTo(url) {
			uni.navigateTo({url});
		},
		
		checkAppIsAudit() {
			that.appIsAudit = app.globalData.checkAppIsAudit();
		},
		initIndexExamData() {
			that.currentCity = cache.getCurrentCityData();
			that.currentExam = cache.getCurrentExamData();
			that.currentProfession = cache.getCurrentProfessionData();
			that.changeExamInfo();
		},
		async changeExamInfo() {
			let exam_id = helper.variableDefalut(that.currentExam.id, 0),
				region_id = helper.variableDefalut(that.currentCity.id, 0),
				profession_id = helper.variableDefalut(that.currentProfession.id, 0);
			if (region_id == 0) {
				that.goTo('city/city?first_visit=1');
				return;
			}
			if (profession_id && app.globalData.isLogin) {
				let user = cache.getUserInfoData(),
					user_profession_id = helper
					.variableDefalut(user.profession_id, 0);
				if (user_profession_id != profession_id) {
					await app.globalData.service.userUpdate({
						exam_id: exam_id,
						region_id: region_id,
						profession_id: profession_id
					});
					user.profession_id = profession_id;
					cache.setUserInfoData(user);
				}
			}
		},
		
		/**
		 * 获取首页数据
		 */
		getHomeData() {
			that.isLoading = true;
			// 优先加载缓存
			let time = app.globalData.getTimestamp();
			let cacheHomeData = wx.getStorageSync(cache.homeKey)
			console.log(cacheHomeData, time);
			if (cacheHomeData && cacheHomeData.expire >= time) {
				that.images = cacheHomeData.swiper;
				that.menuList = cacheHomeData.menuList
				that.articleList = cacheHomeData.articleList;
				that.showWxGroup = cacheHomeData.appConfig.showWxGroup
				that.showWxGroupText = cacheHomeData.appConfig.showWxGroupText
				that.showIndexShareName = cacheHomeData.appConfig.showIndexShareName
				// 从缓存中获取考试时间数据
				if (cacheHomeData.examTime) {
					that.examTime = cacheHomeData.examTime;
				}
				that.isLoading = false;
				return;
			}

			// 加载接口数据
			let cacheTime = 0;
			app.globalData.server
				.getRequest('home', {})
				.then((res) => {
					that.images = res.data.swiper;
					that.menuList = res.data.menuList;
					that.articleList = res.data.articleList;
					that.showWxGroup = res.data.appConfig.showWxGroup;
					that.showWxGroupText = res.data.appConfig.showWxGroupText;
					that.showIndexShareName = res.data.appConfig.showIndexShareName;
					
					// 获取考试时间数据
					if (res.data.examTime) {
						that.examTime = res.data.examTime;
					}
					
					res.data.expire = time + cacheTime;
					cache.setHomeData(res.data);
					that.checkAppIsAudit();
					that.isLoading = false;
				})
				.catch((e) => {
					console.log(e)
					app.showToast('获取首页数据失败');
					that.isLoading = false;
				});
		}
	}
};