







































































































/* 移动端默认样式 */
.pc-test-page.data-v-7f2ace74 {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}
.header.data-v-7f2ace74 {
	text-align: center;
	padding: 40rpx 0;
	background-color: #007aff;
	color: white;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
}
.title.data-v-7f2ace74 {
	font-size: 32rpx;
	font-weight: bold;
}
.info-section.data-v-7f2ace74 {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.info-item.data-v-7f2ace74 {
	display: flex;
	justify-content: space-between;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #eee;
}
.info-item.data-v-7f2ace74:last-child {
	border-bottom: none;
}
.label.data-v-7f2ace74 {
	font-size: 28rpx;
	color: #666;
}
.value.data-v-7f2ace74 {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}
.demo-section.data-v-7f2ace74 {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.demo-title.data-v-7f2ace74 {
	font-size: 30rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	color: #333;
}
.demo-grid.data-v-7f2ace74 {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}
.demo-item.data-v-7f2ace74 {
	flex: 1;
	min-width: 30%;
	background-color: #f0f8ff;
	padding: 20rpx;
	border-radius: 8rpx;
	text-align: center;
}
.demo-text.data-v-7f2ace74 {
	font-size: 26rpx;
	color: #007aff;
}
.button-section.data-v-7f2ace74 {
	text-align: center;
	padding: 40rpx 0;
}
.test-button.data-v-7f2ace74 {
	background-color: #007aff;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 20rpx 60rpx;
	font-size: 28rpx;
}
/* PC端样式适配 */
@media (min-width: 1024px) {
.pc-test-page.pc-mode.data-v-7f2ace74 {
		max-width: 800px;
		margin: 0 auto;
		padding: 20px;
		background-color: #ffffff;
		box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
		border-radius: 12px;
		margin-top: 20px;
}
.header.data-v-7f2ace74 {
		padding: 30px 0;
		border-radius: 8px;
		margin-bottom: 20px;
}
.title.data-v-7f2ace74 {
		font-size: 24px;
}
.info-section.data-v-7f2ace74, .demo-section.data-v-7f2ace74 {
		padding: 20px;
		border-radius: 8px;
		margin-bottom: 20px;
}
.info-item.data-v-7f2ace74 {
		padding: 12px 0;
}
.label.data-v-7f2ace74, .value.data-v-7f2ace74 {
		font-size: 16px;
}
.demo-title.data-v-7f2ace74 {
		font-size: 18px;
		margin-bottom: 15px;
}
.demo-grid.data-v-7f2ace74 {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
		gap: 15px;
}
.demo-item.data-v-7f2ace74 {
		padding: 15px;
		border-radius: 6px;
}
.demo-text.data-v-7f2ace74 {
		font-size: 14px;
}
.test-button.data-v-7f2ace74 {
		padding: 12px 40px;
		font-size: 16px;
		border-radius: 6px;
		cursor: pointer;
}
.test-button.data-v-7f2ace74:hover {
		background-color: #0056b3;
}
}

