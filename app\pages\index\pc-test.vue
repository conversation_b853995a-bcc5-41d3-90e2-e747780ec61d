<template>
	<view :class="['pc-test-page', { 'pc-mode': isPC, 'pc-weixin': isPCW<PERSON><PERSON>, [`screen-${screenType}`]: true }]">
		<view class="header">
			<text class="title">PC端适配测试页面</text>
		</view>
		
		<view class="info-section">
			<view class="info-item">
				<text class="label">是否PC端:</text>
				<text class="value">{{ isPC ? '是' : '否' }}</text>
			</view>
			<view class="info-item">
				<text class="label">是否微信PC端:</text>
				<text class="value">{{ isPCWeixin ? '是' : '否' }}</text>
			</view>
			<view class="info-item">
				<text class="label">屏幕类型:</text>
				<text class="value">{{ screenType }}</text>
			</view>
			<view class="info-item">
				<text class="label">窗口宽度:</text>
				<text class="value">{{ windowInfo.windowWidth }}px</text>
			</view>
			<view class="info-item">
				<text class="label">窗口高度:</text>
				<text class="value">{{ windowInfo.windowHeight }}px</text>
			</view>
		</view>
		
		<view class="demo-section">
			<view class="demo-title">样式演示</view>
			<view class="demo-grid">
				<view class="demo-item" v-for="(item, index) in demoItems" :key="index">
					<text class="demo-text">{{ item.name }}</text>
				</view>
			</view>
		</view>
		
		<view class="button-section">
			<button class="test-button" @click="goBack">返回首页</button>
		</view>
	</view>
</template>

<script>
let pcAdapter = null;
try {
	pcAdapter = require('../../common/js/pc-adapter.js').default;
} catch (e) {
	console.log('PC适配器加载失败:', e);
}

export default {
	data() {
		return {
			isPC: false,
			isPCWeixin: false,
			screenType: 'mobile',
			windowInfo: {},
			demoItems: [
				{ name: '演示项目1' },
				{ name: '演示项目2' },
				{ name: '演示项目3' },
				{ name: '演示项目4' },
				{ name: '演示项目5' },
				{ name: '演示项目6' }
			]
		};
	},
	onLoad() {
		this.initPCAdapter();
	},
	methods: {
		initPCAdapter() {
			try {
				if (pcAdapter) {
					const pcInfo = pcAdapter.getPCInfo();
					this.isPC = pcInfo.isPC;
					this.isPCWeixin = pcInfo.isPCWeixin;
					this.screenType = pcInfo.screenType;
					this.windowInfo = {
						windowWidth: pcInfo.windowWidth,
						windowHeight: pcInfo.windowHeight
					};
					
					console.log('PC测试页面适配信息:', {
						isPC: this.isPC,
						isPCWeixin: this.isPCWeixin,
						screenType: this.screenType,
						windowInfo: this.windowInfo
					});
				}
			} catch (error) {
				console.error('PC适配器初始化失败:', error);
			}
		},
		goBack() {
			uni.navigateBack();
		}
	}
};
</script>

<style scoped>
/* 移动端默认样式 */
.pc-test-page {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	padding: 40rpx 0;
	background-color: #007aff;
	color: white;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
}

.title {
	font-size: 32rpx;
	font-weight: bold;
}

.info-section {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.info-item {
	display: flex;
	justify-content: space-between;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
	border-bottom: none;
}

.label {
	font-size: 28rpx;
	color: #666;
}

.value {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

.demo-section {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.demo-title {
	font-size: 30rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	color: #333;
}

.demo-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.demo-item {
	flex: 1;
	min-width: 30%;
	background-color: #f0f8ff;
	padding: 20rpx;
	border-radius: 8rpx;
	text-align: center;
}

.demo-text {
	font-size: 26rpx;
	color: #007aff;
}

.button-section {
	text-align: center;
	padding: 40rpx 0;
}

.test-button {
	background-color: #007aff;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 20rpx 60rpx;
	font-size: 28rpx;
}

/* PC端样式适配 */
@media (min-width: 1024px) {
	.pc-test-page.pc-mode {
		max-width: 800px;
		margin: 0 auto;
		padding: 20px;
		background-color: #ffffff;
		box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
		border-radius: 12px;
		margin-top: 20px;
	}
	
	.header {
		padding: 30px 0;
		border-radius: 8px;
		margin-bottom: 20px;
	}
	
	.title {
		font-size: 24px;
	}
	
	.info-section, .demo-section {
		padding: 20px;
		border-radius: 8px;
		margin-bottom: 20px;
	}
	
	.info-item {
		padding: 12px 0;
	}
	
	.label, .value {
		font-size: 16px;
	}
	
	.demo-title {
		font-size: 18px;
		margin-bottom: 15px;
	}
	
	.demo-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
		gap: 15px;
	}
	
	.demo-item {
		padding: 15px;
		border-radius: 6px;
	}
	
	.demo-text {
		font-size: 14px;
	}
	
	.test-button {
		padding: 12px 40px;
		font-size: 16px;
		border-radius: 6px;
		cursor: pointer;
	}
	
	.test-button:hover {
		background-color: #0056b3;
	}
}
</style>
