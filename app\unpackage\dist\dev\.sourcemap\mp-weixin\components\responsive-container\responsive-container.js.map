{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/responsive-container/responsive-container.vue?0ffc", "webpack:///D:/桌面/thinker/app/components/responsive-container/responsive-container.vue?0724", "webpack:///D:/桌面/thinker/app/components/responsive-container/responsive-container.vue?dfdc", "webpack:///D:/桌面/thinker/app/components/responsive-container/responsive-container.vue?0a34", "uni-app:///components/responsive-container/responsive-container.vue", "webpack:///D:/桌面/thinker/app/components/responsive-container/responsive-container.vue?dc52", "webpack:///D:/桌面/thinker/app/components/responsive-container/responsive-container.vue?b75d"], "names": ["name", "props", "max<PERSON><PERSON><PERSON>", "type", "default", "mobile", "tablet", "desktop", "large", "padding", "margin", "center", "background", "fluid", "pcOptimized", "shadow", "radius", "data", "adapterInfo", "platform", "device", "screenType", "computed", "containerClasses", "classes", "containerStyles", "styles", "Object", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "updateAdapterInfo", "setupEventListeners", "multiPlatformAdapter", "removeEventListeners", "getResponsiveValue", "formatValue"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6I;AAC7I;AACwE;AACL;AACsC;;;AAGzG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,2GAAM;AACR,EAAE,oHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwqB,CAAgB,sqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACO5rB;;;;;;;gBAEA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;QAAA;UACAC;UACAC;UACAC;UACAC;QACA;MAAA;IACA;IAEA;IACAC;MACAN;MACAC;QAAA;UACAC;UACAC;UACAC;UACAC;QACA;MAAA;IACA;IAEA;IACAE;MACAP;MACAC;IACA;IAEA;IACAO;MACAR;MACAC;IACA;IAEA;IACAQ;MACAT;MACAC;IACA;IAEA;IACAS;MACAV;MACAC;IACA;IAEA;IACAU;MACAX;MACAC;IACA;IAEA;IACAW;MACAZ;MACAC;IACA;IAEA;IACAY;MACAb;MACAC;IACA;EACA;EACAa;IACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACAC;MACA;;MAEA;MACAC;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;MAEA;QACAA;MACA;MAEA;QACAA;MACA;MAEA;QACAA;MACA;;MAEA;MACA;QACA;UACAA;QACA;UACA;UACA;YACAA;UACA;QACA;MACA;MAEA;IACA;IAEAC;MACA;;MAEA;MACA;QACA;QACA;UACAC;QACA;MACA;;MAEA;MACA;MACA;QACAA;MACA;;MAEA;MACA;MACA;QACAA;MACA;;MAEA;MACA;MACA;QACA;UACAA;QACA;UACAC;QACA;MACA;;MAEA;MACA;MACA;QACAD;MACA;MAEA;IACA;EACA;EAEAE;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;IAEAC;MAAA;MACA;QACA;MACA;MAEAC;IACA;IAEAC;MACA;QACAD;MACA;IACA;IAEAE;MACA;IACA;IAEAC;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACpNA;AAAA;AAAA;AAAA;AAAuxC,CAAgB,6sCAAG,EAAC,C;;;;;;;;;;;ACA3yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/responsive-container/responsive-container.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./responsive-container.vue?vue&type=template&id=65a324f8&scoped=true&\"\nvar renderjs\nimport script from \"./responsive-container.vue?vue&type=script&lang=js&\"\nexport * from \"./responsive-container.vue?vue&type=script&lang=js&\"\nimport style0 from \"./responsive-container.vue?vue&type=style&index=0&id=65a324f8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"65a324f8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/responsive-container/responsive-container.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-container.vue?vue&type=template&id=65a324f8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-container.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-container.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"responsive-container\" :class=\"containerClasses\" :style=\"containerStyles\">\n    <slot></slot>\n  </view>\n</template>\n\n<script>\nimport multiPlatformAdapter from '@/common/js/pc-adapter.js'\n\nexport default {\n  name: 'ResponsiveContainer',\n  props: {\n    // 容器最大宽度配置\n    maxWidth: {\n      type: [String, Number, Object],\n      default: () => ({\n        mobile: '100%',\n        tablet: '750px',\n        desktop: '1000px',\n        large: '1200px'\n      })\n    },\n\n    // 内边距配置\n    padding: {\n      type: [String, Number, Object],\n      default: () => ({\n        mobile: '16px',\n        tablet: '24px',\n        desktop: '32px',\n        large: '40px'\n      })\n    },\n\n    // 外边距配置\n    margin: {\n      type: [String, Number, Object],\n      default: null\n    },\n\n    // 是否居中\n    center: {\n      type: Boolean,\n      default: true\n    },\n\n    // 背景配置\n    background: {\n      type: [String, Object],\n      default: null\n    },\n\n    // 是否为流体容器（不限制最大宽度）\n    fluid: {\n      type: Boolean,\n      default: false\n    },\n\n    // 是否启用PC端优化\n    pcOptimized: {\n      type: Boolean,\n      default: true\n    },\n\n    // 是否启用阴影\n    shadow: {\n      type: [Boolean, String, Object],\n      default: false\n    },\n\n    // 边框圆角\n    radius: {\n      type: [String, Number, Object],\n      default: null\n    }\n  },\n  data() {\n    return {\n      adapterInfo: {\n        platform: {},\n        device: {},\n        screenType: 'mobile'\n      }\n    }\n  },\n\n  computed: {\n    containerClasses() {\n      const classes = ['responsive-container']\n\n      // 屏幕类型类\n      classes.push('responsive-container--' + this.adapterInfo.device.screenType)\n\n      // 平台类\n      if (this.adapterInfo.platform.current) {\n        classes.push('responsive-container--platform-' + this.adapterInfo.platform.current)\n      }\n\n      // 功能类\n      if (this.center) {\n        classes.push('responsive-container--center')\n      }\n\n      if (this.fluid) {\n        classes.push('responsive-container--fluid')\n      }\n\n      if (this.adapterInfo.platform.isPC) {\n        classes.push('responsive-container--pc')\n      }\n\n      if (this.pcOptimized && this.adapterInfo.platform.isPC) {\n        classes.push('responsive-container--pc-optimized')\n      }\n\n      // 阴影类\n      if (this.shadow) {\n        if (typeof this.shadow === 'boolean') {\n          classes.push('responsive-container--shadow')\n        } else {\n          const shadowValue = this.shadow\n          if (shadowValue) {\n            classes.push('responsive-container--shadow-' + shadowValue)\n          }\n        }\n      }\n\n      return classes\n    },\n    \n    containerStyles() {\n      const styles = {}\n\n      // 最大宽度\n      if (!this.fluid) {\n        const maxWidth = this.maxWidth\n        if (maxWidth) {\n          styles.maxWidth = this.formatValue(maxWidth)\n        }\n      }\n\n      // 内边距\n      const padding = this.padding\n      if (padding) {\n        styles.padding = this.formatValue(padding)\n      }\n\n      // 外边距\n      const margin = this.margin\n      if (margin) {\n        styles.margin = this.formatValue(margin)\n      }\n\n      // 背景\n      const background = this.background\n      if (background) {\n        if (typeof background === 'string') {\n          styles.backgroundColor = background\n        } else if (typeof background === 'object') {\n          Object.assign(styles, background)\n        }\n      }\n\n      // 边框圆角\n      const radius = this.radius\n      if (radius) {\n        styles.borderRadius = this.formatValue(radius)\n      }\n\n      return styles\n    }\n  },\n\n  mounted() {\n    this.updateAdapterInfo()\n    this.setupEventListeners()\n  },\n\n  beforeDestroy() {\n    this.removeEventListeners()\n  },\n\n  methods: {\n    updateAdapterInfo() {\n      this.adapterInfo = multiPlatformAdapter.getAdapterInfo()\n    },\n\n    setupEventListeners() {\n      this.resizeHandler = () => {\n        this.updateAdapterInfo()\n      }\n\n      multiPlatformAdapter.on('resize', this.resizeHandler)\n    },\n\n    removeEventListeners() {\n      if (this.resizeHandler) {\n        multiPlatformAdapter.off('resize', this.resizeHandler)\n      }\n    },\n\n    getResponsiveValue(value) {\n      return multiPlatformAdapter.getResponsiveValue(value)\n    },\n\n    formatValue(value) {\n      if (typeof value === 'number') {\n        return value + 'px'\n      }\n      return value\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/common/css/pc-responsive.scss';\n\n.responsive-container {\n  width: 100%;\n  box-sizing: border-box;\n  transition: all 0.3s ease;\n\n  &--center {\n    margin-left: auto;\n    margin-right: auto;\n  }\n\n  &--fluid {\n    max-width: none !important;\n  }\n\n  // 平台特定样式\n  &--platform-h5 {\n    // H5特定样式\n  }\n\n  &--platform-app {\n    // App特定样式\n  }\n\n  &--platform-mp-weixin {\n    // 微信小程序特定样式\n  }\n\n  // PC端样式\n  &--pc {\n    @include pc-only {\n      min-height: auto;\n    }\n  }\n\n  &--pc-optimized {\n    @include pc-only {\n      background: #fff;\n      border-radius: 8px;\n      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n\n      &:hover {\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n      }\n    }\n  }\n\n  // 阴影样式\n  &--shadow {\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  }\n\n  &--shadow-sm {\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n  }\n\n  &--shadow-md {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  }\n\n  &--shadow-lg {\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\n  }\n\n  &--shadow-xl {\n    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);\n  }\n}\n\n// 响应式样式\n.responsive-container--mobile {\n  @include respond-to(mobile) {\n    padding: 16px;\n  }\n}\n\n.responsive-container--tablet {\n  @include respond-to(tablet) {\n    padding: 24px;\n  }\n}\n\n.responsive-container--desktop {\n  @include respond-to(desktop) {\n    padding: 32px;\n  }\n}\n\n.responsive-container--large {\n  @include respond-to(large) {\n    padding: 40px;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-container.vue?vue&type=style&index=0&id=65a324f8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-container.vue?vue&type=style&index=0&id=65a324f8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753619717358\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}