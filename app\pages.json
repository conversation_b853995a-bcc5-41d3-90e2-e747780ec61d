{
	"pages": [{
			"path": "pages/index/city/city",
			"style": {}
		},
		{
			"path": "pages/index/sso/sso",
			"style": {}
		},
		{
			"path": "pages/index/profession/school",
			"style": {}
		},
		{
			"path": "pages/index/index",
			"style": {}
		},
		{
			"path": "pages/index/pc-test",
			"style": {
				"navigationBarTitleText": "PC端适配测试"
			}
		},
		{
			"path": "pages/index/exam/exam",
			"style": {}
		},
		{
			"path": "pages/search/search",
			"style": {}
		},
		{
			"path": "pages/search/list"
		},
		{
			"path": "pages/pay/pay",
			"style": {}
		},
		{
			"path": "pages/pay/payRes",
			"style": {}
		},
		{
			"path": "pages/practice/course/detail",
			"style": {}
		},
		{
			"path": "pages/practice/paper/category",
			"style": {}
		},
		{
			"path": "pages/practice/paper/paper",
			"style": {}
		},
		{
			"path": "pages/practice/upload/upload",
			"style": {}
		},
		{
			"path": "pages/practice/course/buy",
			"style": {}
		},
		{
			"path": "pages/practice/thesis/thesis",
			"style": {}
		},
		{
			"path": "pages/practice/question/detail",
			"style": {}
		},
		{
			"path": "pages/user/user",
			"style": {}
		},
		{
			"path": "pages/user/about/about",
			"style": {}
		},
		{
			"path": "pages/user/switch/switch",
			"style": {}
		},
		{
			"path": "pages/user/opinion/opinion",
			"style": {}
		},
		{
			"path": "pages/index/article/list",
			"style": {}
		},
		{
			"path": "pages/index/article/detail",
			"style": {}
		},
		{
			"path": "pages/user/login/login",
			"style": {}
		},
		{
			"path": "pages/user/login/auth",
			"style": {}
		},
		{
			"path": "pages/user/info/info",
			"style": {}
		},
		{
			"path": "pages/user/sign/sign",
			"style": {}
		},
		{
			"path": "pages/user/sign/group",
			"style": {}
		},
		{
			"path": "pages/user/course/course",
			"style": {}
		},
		{
			"path": "pages/practice/course/course",
			"style": {}
		},
		{
			"path": "pages/index/profession/profession",
			"style": {}
		},
		{
			"path": "pages/user/register/register",
			"style": {}
		},
		{
			"path": "pages/user/register/forget",
			"style": {}
		},
		{
			"path": "pages/user/info/edit",
			"style": {}
		},
		{
			"path": "pages/practice/topic/topic",
			"style": {}
		},
		{
			"path": "pages/practice/error/error",
			"style": {}
		},
		{
			"path": "pages/practice/coll/coll",
			"style": {}
		},
		{
			"path": "pages/practice/material/list",
			"style": {}
		},
		{
			"path": "pages/practice/material/detail",
			"style": {}
		},
		{
			"path": "pages/practice/material/share",
			"style": {}
		},
		{
			"path": "pages/practice/material/record",
			"style": {}
		},
		{
			"path": "pages/practice/vip/vip",
			"style": {
				"navigationBarTextStyle": "white",
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/practice/vip/detail"
		},
		{
			"path": "pages/practice/practice",
			"style": {}
		},
		{
			"path": "pages/practice/result/result",
			"style": {}
		},
		{
			"path": "pages/practice/feedback/feedback",
			"style": {}
		},
		{
			"path": "pages/user/login/bindemail",
			"style": {}
		},
		{
			"path": "pages/user/login/bindmobile",
			"style": {}
		},
		{
			"path": "pages/user/login/bindmp",
			"style": {}
		},
		{
			"path": "pages/user/login/bindopenid",
			"style": {}
		},
		{
			"path": "pages/user/set/password",
			"style": {}
		},
		{
			"path": "pages/user/promote/promote",
			"style": {}
		},
		{
			"path": "pages/user/promote/group"
		},
		{
			"path": "pages/user/promote/completedorder",
			"style": {}
		},
		{
			"path": "pages/user/promote/settlement",
			"style": {}
		},
		{
			"path": "pages/user/ranking/ranking",
			"style": {}
		},
		{
			"path": "pages/user/vip/vip",
			"style": {}
		},
		{
			"path": "pages/user/vip/transfer",
			"style": {}
		},
		{
			"path": "pages/user/group/group",
			"style": {}
		}
	],
	"embeddedAppIdList": [
		"wxd9634afb01b983c0"
	],
	"tabBar": {
		"color": "#7A7E83",
		"selectedColor": "#6097FE",
		"list": [{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "static/img/ic_index_normal.png",
				"selectedIconPath": "static/img/ic_index_selected.png"
			},
			{
				"pagePath": "pages/search/search",
				"text": "搜索",
				"iconPath": "static/img/search_normal.png",
				"selectedIconPath": "static/img/search_select.png"
			},
			{
				"pagePath": "pages/user/user",
				"text": "我的",
				"iconPath": "static/img/ic_user_normal.png",
				"selectedIconPath": "static/img/ic_user_selected.png"
			}
		]
	},
	"sitemapLocation": "sitemap.json",
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationStyle": "custom",
		"navigationBarBackgroundColor": "#FFFFFF",
		"navigationBarTitleText": ""
	},
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "", //模式名称
			"path": "", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	},
	"navigationStyle":"default",
	"subPackages": []
}