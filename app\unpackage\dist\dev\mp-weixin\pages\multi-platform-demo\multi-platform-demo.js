(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/multi-platform-demo/multi-platform-demo"],{

/***/ 528:
/*!**********************************************************************************************!*\
  !*** D:/桌面/thinker/app/main.js?{"page":"pages%2Fmulti-platform-demo%2Fmulti-platform-demo"} ***!
  \**********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _multiPlatformDemo = _interopRequireDefault(__webpack_require__(/*! ./pages/multi-platform-demo/multi-platform-demo.vue */ 529));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_multiPlatformDemo.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 529:
/*!***************************************************************************!*\
  !*** D:/桌面/thinker/app/pages/multi-platform-demo/multi-platform-demo.vue ***!
  \***************************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

throw new Error("Module build failed (from ./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js):\nError: ENOENT: no such file or directory, open 'D:\\桌面\\thinker\\app\\pages\\multi-platform-demo\\multi-platform-demo.vue'");

/***/ })

},[[528,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/multi-platform-demo/multi-platform-demo.js.map