{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/index/pc-test.vue?8477", "webpack:///D:/桌面/thinker/app/pages/index/pc-test.vue?5f39", "webpack:///D:/桌面/thinker/app/pages/index/pc-test.vue?5baf", "uni-app:///pages/index/pc-test.vue", "webpack:///D:/桌面/thinker/app/pages/index/pc-test.vue?e679", "webpack:///D:/桌面/thinker/app/pages/index/pc-test.vue?62a8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "pcAdapter", "console", "data", "isPC", "isPCWeixin", "screenType", "windowInfo", "demoItems", "name", "onLoad", "methods", "initPCAdapter", "windowWidth", "windowHeight", "goBack", "uni"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACqC;;;AAG3F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAA2pB,CAAgB,ypBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6C/qB;AACA;EACAC;AACA;EACAC;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC,YACA;QAAAC;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA;IAEA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;UACA;UACA;UACA;UACA;UACA;YACAC;YACAC;UACA;UAEAZ;YACAE;YACAC;YACAC;YACAC;UACA;QACA;MACA;QACAL;MACA;IACA;IACAa;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpGA;AAAA;AAAA;AAAA;AAA49B,CAAgB,s7BAAG,EAAC,C;;;;;;;;;;;ACAh/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/pc-test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/pc-test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pc-test.vue?vue&type=template&id=7f2ace74&scoped=true&\"\nvar renderjs\nimport script from \"./pc-test.vue?vue&type=script&lang=js&\"\nexport * from \"./pc-test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pc-test.vue?vue&type=style&index=0&id=7f2ace74&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7f2ace74\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/pc-test.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-test.vue?vue&type=template&id=7f2ace74&scoped=true&\"", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-test.vue?vue&type=script&lang=js&\"", "<template>\n\t<view :class=\"['pc-test-page', { 'pc-mode': isPC, 'pc-weixin': isPCW<PERSON><PERSON>, [`screen-${screenType}`]: true }]\">\n\t\t<view class=\"header\">\n\t\t\t<text class=\"title\">PC端适配测试页面</text>\n\t\t</view>\n\t\t\n\t\t<view class=\"info-section\">\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"label\">是否PC端:</text>\n\t\t\t\t<text class=\"value\">{{ isPC ? '是' : '否' }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"label\">是否微信PC端:</text>\n\t\t\t\t<text class=\"value\">{{ isPCWeixin ? '是' : '否' }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"label\">屏幕类型:</text>\n\t\t\t\t<text class=\"value\">{{ screenType }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"label\">窗口宽度:</text>\n\t\t\t\t<text class=\"value\">{{ windowInfo.windowWidth }}px</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"label\">窗口高度:</text>\n\t\t\t\t<text class=\"value\">{{ windowInfo.windowHeight }}px</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"demo-section\">\n\t\t\t<view class=\"demo-title\">样式演示</view>\n\t\t\t<view class=\"demo-grid\">\n\t\t\t\t<view class=\"demo-item\" v-for=\"(item, index) in demoItems\" :key=\"index\">\n\t\t\t\t\t<text class=\"demo-text\">{{ item.name }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"button-section\">\n\t\t\t<button class=\"test-button\" @click=\"goBack\">返回首页</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nlet pcAdapter = null;\ntry {\n\tpcAdapter = require('../../common/js/pc-adapter.js').default;\n} catch (e) {\n\tconsole.log('PC适配器加载失败:', e);\n}\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tisPC: false,\n\t\t\tisPCWeixin: false,\n\t\t\tscreenType: 'mobile',\n\t\t\twindowInfo: {},\n\t\t\tdemoItems: [\n\t\t\t\t{ name: '演示项目1' },\n\t\t\t\t{ name: '演示项目2' },\n\t\t\t\t{ name: '演示项目3' },\n\t\t\t\t{ name: '演示项目4' },\n\t\t\t\t{ name: '演示项目5' },\n\t\t\t\t{ name: '演示项目6' }\n\t\t\t]\n\t\t};\n\t},\n\tonLoad() {\n\t\tthis.initPCAdapter();\n\t},\n\tmethods: {\n\t\tinitPCAdapter() {\n\t\t\ttry {\n\t\t\t\tif (pcAdapter) {\n\t\t\t\t\tconst pcInfo = pcAdapter.getPCInfo();\n\t\t\t\t\tthis.isPC = pcInfo.isPC;\n\t\t\t\t\tthis.isPCWeixin = pcInfo.isPCWeixin;\n\t\t\t\t\tthis.screenType = pcInfo.screenType;\n\t\t\t\t\tthis.windowInfo = {\n\t\t\t\t\t\twindowWidth: pcInfo.windowWidth,\n\t\t\t\t\t\twindowHeight: pcInfo.windowHeight\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('PC测试页面适配信息:', {\n\t\t\t\t\t\tisPC: this.isPC,\n\t\t\t\t\t\tisPCWeixin: this.isPCWeixin,\n\t\t\t\t\t\tscreenType: this.screenType,\n\t\t\t\t\t\twindowInfo: this.windowInfo\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('PC适配器初始化失败:', error);\n\t\t\t}\n\t\t},\n\t\tgoBack() {\n\t\t\tuni.navigateBack();\n\t\t}\n\t}\n};\n</script>\n\n<style scoped>\n/* 移动端默认样式 */\n.pc-test-page {\n\tpadding: 20rpx;\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n}\n\n.header {\n\ttext-align: center;\n\tpadding: 40rpx 0;\n\tbackground-color: #007aff;\n\tcolor: white;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n\n.info-section {\n\tbackground-color: white;\n\tborder-radius: 12rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.info-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tpadding: 15rpx 0;\n\tborder-bottom: 1rpx solid #eee;\n}\n\n.info-item:last-child {\n\tborder-bottom: none;\n}\n\n.label {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.value {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n}\n\n.demo-section {\n\tbackground-color: white;\n\tborder-radius: 12rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.demo-title {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tmargin-bottom: 20rpx;\n\tcolor: #333;\n}\n\n.demo-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 15rpx;\n}\n\n.demo-item {\n\tflex: 1;\n\tmin-width: 30%;\n\tbackground-color: #f0f8ff;\n\tpadding: 20rpx;\n\tborder-radius: 8rpx;\n\ttext-align: center;\n}\n\n.demo-text {\n\tfont-size: 26rpx;\n\tcolor: #007aff;\n}\n\n.button-section {\n\ttext-align: center;\n\tpadding: 40rpx 0;\n}\n\n.test-button {\n\tbackground-color: #007aff;\n\tcolor: white;\n\tborder: none;\n\tborder-radius: 8rpx;\n\tpadding: 20rpx 60rpx;\n\tfont-size: 28rpx;\n}\n\n/* PC端样式适配 */\n@media (min-width: 1024px) {\n\t.pc-test-page.pc-mode {\n\t\tmax-width: 800px;\n\t\tmargin: 0 auto;\n\t\tpadding: 20px;\n\t\tbackground-color: #ffffff;\n\t\tbox-shadow: 0 0 20px rgba(0, 0, 0, 0.1);\n\t\tborder-radius: 12px;\n\t\tmargin-top: 20px;\n\t}\n\t\n\t.header {\n\t\tpadding: 30px 0;\n\t\tborder-radius: 8px;\n\t\tmargin-bottom: 20px;\n\t}\n\t\n\t.title {\n\t\tfont-size: 24px;\n\t}\n\t\n\t.info-section, .demo-section {\n\t\tpadding: 20px;\n\t\tborder-radius: 8px;\n\t\tmargin-bottom: 20px;\n\t}\n\t\n\t.info-item {\n\t\tpadding: 12px 0;\n\t}\n\t\n\t.label, .value {\n\t\tfont-size: 16px;\n\t}\n\t\n\t.demo-title {\n\t\tfont-size: 18px;\n\t\tmargin-bottom: 15px;\n\t}\n\t\n\t.demo-grid {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n\t\tgap: 15px;\n\t}\n\t\n\t.demo-item {\n\t\tpadding: 15px;\n\t\tborder-radius: 6px;\n\t}\n\t\n\t.demo-text {\n\t\tfont-size: 14px;\n\t}\n\t\n\t.test-button {\n\t\tpadding: 12px 40px;\n\t\tfont-size: 16px;\n\t\tborder-radius: 6px;\n\t\tcursor: pointer;\n\t}\n\t\n\t.test-button:hover {\n\t\tbackground-color: #0056b3;\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-test.vue?vue&type=style&index=0&id=7f2ace74&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-test.vue?vue&type=style&index=0&id=7f2ace74&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753620500467\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}