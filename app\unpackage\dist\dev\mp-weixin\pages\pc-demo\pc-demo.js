(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/pc-demo/pc-demo"],{

/***/ 520:
/*!**********************************************************************!*\
  !*** D:/桌面/thinker/app/main.js?{"page":"pages%2Fpc-demo%2Fpc-demo"} ***!
  \**********************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _pcDemo = _interopRequireDefault(__webpack_require__(/*! ./pages/pc-demo/pc-demo.vue */ 521));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_pcDemo.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 521:
/*!***************************************************!*\
  !*** D:/桌面/thinker/app/pages/pc-demo/pc-demo.vue ***!
  \***************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

throw new Error("Module build failed (from ./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js):\nError: ENOENT: no such file or directory, open 'D:\\桌面\\thinker\\app\\pages\\pc-demo\\pc-demo.vue'");

/***/ })

},[[520,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/pc-demo/pc-demo.js.map